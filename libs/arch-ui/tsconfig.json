{"compilerOptions": {"module": "ESNext", "esModuleInterop": true, "target": "es6", "lib": ["es5", "es6", "es7", "es2017", "dom"], "sourceMap": true, "allowJs": true, "jsx": "react-jsx", "moduleResolution": "node", "baseUrl": "src", "forceConsistentCasingInFileNames": true, "declaration": false, "strict": true, "noEmit": true, "skipLibCheck": true, "types": ["jest", "@testing-library/jest-dom"]}, "include": ["jest.config.js", ".eslintrc.js", "src"], "exclude": ["node_modules", "scripts"]}