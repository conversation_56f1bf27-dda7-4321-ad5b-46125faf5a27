import type { IssueDetailsBasicSchema } from "@shape-construction/api/src/types";
import Popover from "@shape-construction/arch-ui/src/Popover";
import { ProgressBarRoot } from "@shape-construction/arch-ui/src/ProgressBar/ProgressBar";
import { breakpoints } from "@shape-construction/arch-ui/src/utils/breakpoints";
import { cn } from "@shape-construction/arch-ui/src/utils/classes";
import { useMediaQuery } from "@shape-construction/hooks";
import { useState } from "react";

type IssueQualityIndicatorProps = {
    className?: string;
    qualityScore: IssueDetailsBasicSchema['qualityScore'];
};

function getProgressBarColor(qualityScore: number) {
    if (qualityScore < 19) return 'danger';
    if (qualityScore >= 19 || qualityScore < 30) return 'warning';
}

export const IssueQualityIndicator: React.FC<IssueQualityIndicatorProps> = ({ className, qualityScore }) => {
    const [issueQualityPopoverOpen, setIssueQualityPopoverOpen] = useState(false);
    const isLargeScreen = useMediaQuery(breakpoints.up('md'));
    if (qualityScore === null || qualityScore > 30) {
        return <div className={className} />
    };
    return (
        <div className={cn(className, 'flex justify-center items-center rounded-full w-auto p-0.5 hover:bg-neutral-subtle')}
        
        >
            <Popover open={issueQualityPopoverOpen} onOpenChange={setIssueQualityPopoverOpen}>
                <Popover.Trigger>
                    <ProgressBarRoot progress={qualityScore} size="small" color={getProgressBarColor(qualityScore)} variant='donut' />
                </Popover.Trigger>
                <Popover.Content side="bottom" align="start" sideOffset={10} className="md:max-h-50vh overflow-y-auto p-0">
                    <div className="flex flex-col gap-1 text-sm leading-6 font-medium">
                        <span>Not ideal</span>
                        <span className="font-bold">23</span>
                        <span className="font-normal">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Perferendis quis eius debitis modi consequuntur recusandae id. Aliquid minima eius obcaecati?
                        </span>
                    </div>
                </Popover.Content>
            </Popover>
        </div>
    );
};
